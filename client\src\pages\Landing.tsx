import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Bo<PERSON> } from "lucide-react";
import { useAuth0Integration } from "@/hooks/useAuth0";
import { useState } from "react";

export default function Landing() {
  const { login, isLoading } = useAuth0Integration();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showForm, setShowForm] = useState(false);

  const handleLogin = async () => {
    if (!showForm) {
      setShowForm(true);
      return;
    }
    
    if (email && password) {
      await login(email, password);
    }
  };

  const handleQuickDemo = () => {
    login("<EMAIL>", "demo123");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-purple-600 to-purple-800 flex flex-col justify-center items-center px-6 py-8">
      <div className="w-full max-w-sm mx-auto text-center">
        {/* Logo/Brand Section */}
        <div className="mb-8 animate-fadeIn">
          <div className="w-20 h-20 mx-auto mb-4 bg-white rounded-2xl shadow-lg flex items-center justify-center">
            <Bot className="w-10 h-10 text-green-500" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">ChatGPT Clone</h1>
          <p className="text-white/80 text-lg">AI Assistant</p>
        </div>

        {/* Hero Illustration */}
        <div className="mb-8 bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
          <div className="text-6xl mb-4">💬</div>
          <p className="text-white/90 text-base leading-relaxed">
            Get Started with AI conversations and image generation
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          {showForm && (
            <div className="space-y-3 mb-4">
              <Input
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full py-3 px-4 rounded-xl bg-white/90 text-gray-800 placeholder-gray-500"
              />
              <Input
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full py-3 px-4 rounded-xl bg-white/90 text-gray-800 placeholder-gray-500"
              />
            </div>
          )}
          
          <Button
            onClick={handleLogin}
            disabled={isLoading}
            className="w-full bg-white text-gray-800 font-semibold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50"
          >
            {isLoading ? "Signing In..." : showForm ? "Sign In" : "Get Started"}
          </Button>

          <Button
            onClick={handleQuickDemo}
            disabled={isLoading}
            variant="outline"
            className="w-full bg-white/20 backdrop-blur-sm border border-white/30 text-white font-semibold py-4 px-6 rounded-xl hover:bg-white/30 transition-all duration-200 disabled:opacity-50"
          >
            Try Demo
          </Button>
        </div>
      </div>
    </div>
  );
}
