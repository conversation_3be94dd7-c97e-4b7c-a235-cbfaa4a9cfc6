import { useEffect, useState } from "react";
import { <PERSON> } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Menu, Settings, User, MessageCircle, Image, Code } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ChatListItem } from "@/components/ChatListItem";
import { useAuth } from "@/hooks/useAuth";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { Chat } from "@shared/schema";

export default function Dashboard() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: chats = [], isLoading } = useQuery<Chat[]>({
    queryKey: ["/api/chats"],
  });

  const createChatMutation = useMutation({
    mutationFn: async (data: { title: string; type: "text" | "image" | "code" }) => {
      const response = await apiRequest("POST", "/api/chats", data);
      return response.json();
    },
    onSuccess: (newChat) => {
      queryClient.invalidateQueries({ queryKey: ["/api/chats"] });
      window.location.href = `/chat/${newChat.id}`;
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create new chat",
        variant: "destructive",
      });
    },
  });

  const deleteChatMutation = useMutation({
    mutationFn: async (chatId: number) => {
      await apiRequest("DELETE", `/api/chats/${chatId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/chats"] });
      toast({
        title: "Deleted",
        description: "Chat deleted successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete chat",
        variant: "destructive",
      });
    },
  });

  const handleNewChat = () => {
    createChatMutation.mutate({
      title: "New Chat",
      type: "text",
    });
  };

  const handleQuickAction = (type: "text" | "image" | "code") => {
    const titles = {
      text: "New Chat",
      image: "Image Generation",
      code: "Code Assistant",
    };
    
    createChatMutation.mutate({
      title: titles[type],
      type,
    });
  };

  const handleSelectChat = (chatId: number) => {
    window.location.href = `/chat/${chatId}`;
  };

  const handleDeleteChat = (chatId: number) => {
    deleteChatMutation.mutate(chatId);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading chats...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center space-x-3">
            <Button variant="ghost" size="sm" className="p-2">
              <Menu className="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </Button>
            <h1 className="text-xl font-bold text-gray-800 dark:text-white">ChatGPT</h1>
          </div>
          <div className="flex items-center space-x-2">
            <Link href="/settings">
              <Button variant="ghost" size="sm" className="p-2">
                <Settings className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              </Button>
            </Link>
            <Button variant="ghost" size="sm" className="p-2">
              <User className="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="px-4 py-6">
        {/* New Chat CTA */}
        <Button
          onClick={handleNewChat}
          disabled={createChatMutation.isPending}
          className="w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-4 px-6 rounded-xl shadow-lg mb-6 transition-all duration-200 transform hover:scale-105 disabled:transform-none"
        >
          <Plus className="w-5 h-5 mr-2" />
          {createChatMutation.isPending ? "Creating..." : "New Chat"}
        </Button>

        {/* Quick Actions */}
        <div className="grid grid-cols-3 gap-3 mb-6">
          <Button
            variant="outline"
            onClick={() => handleQuickAction("text")}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl p-4 text-center hover:shadow-md transition-all duration-200 flex flex-col items-center space-y-2"
          >
            <MessageCircle className="w-6 h-6 text-blue-500" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Text Chat</span>
          </Button>

          <Button
            variant="outline"
            onClick={() => handleQuickAction("image")}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl p-4 text-center hover:shadow-md transition-all duration-200 flex flex-col items-center space-y-2"
          >
            <Image className="w-6 h-6 text-purple-500" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Generate Image</span>
          </Button>

          <Button
            variant="outline"
            onClick={() => handleQuickAction("code")}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl p-4 text-center hover:shadow-md transition-all duration-200 flex flex-col items-center space-y-2"
          >
            <Code className="w-6 h-6 text-green-500" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Code Help</span>
          </Button>
        </div>

        {/* Recent Conversations */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
            Recent Conversations
          </h2>

          {chats.length === 0 ? (
            <div className="text-center py-8">
              <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">No conversations yet</p>
              <p className="text-sm text-gray-400 dark:text-gray-500">
                Start a new chat to begin
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {chats.map((chat) => (
                <ChatListItem
                  key={chat.id}
                  id={chat.id}
                  title={chat.title}
                  lastMessage="Click to continue conversation..."
                  timestamp={chat.updatedAt || chat.createdAt || new Date().toISOString()}
                  type={chat.type}
                  onSelect={handleSelectChat}
                  onDelete={handleDeleteChat}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
