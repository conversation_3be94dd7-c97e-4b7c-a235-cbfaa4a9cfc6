import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://qoqylojczbmncechnwnz.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFvcXlsb2pjemJtbmNlY2hud256Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTU0OTUxOCwiZXhwIjoyMDY1MTI1NTE4fQ.Y1hHVfu4PvKEyGc5hbwkVJS3Z5gfz3jeAngsa3gg--s'

export const supabase = createClient(supabaseUrl, supabaseServiceKey)

export interface SupabaseUser {
  id: string
  email: string
  first_name?: string
  last_name?: string
  profile_image_url?: string
  created_at: string
  updated_at: string
}

export interface SupabaseChat {
  id: number
  user_id: string
  title: string
  type: 'text' | 'image' | 'code'
  created_at: string
  updated_at: string
}

export interface SupabaseMessage {
  id: number
  chat_id: number
  role: 'user' | 'assistant'
  content: string
  image_url?: string
  metadata?: any
  created_at: string
}

export interface SupabaseUserSettings {
  id: number
  user_id: string
  dark_mode: boolean
  auto_save_chats: boolean
  send_on_enter: boolean
  openai_api_key?: string
  created_at: string
  updated_at: string
}

export class SupabaseStorage {
  // Initialize database tables if they don't exist
  async initializeTables() {
    try {
      // Create profiles table
      await supabase.rpc('create_profiles_table', {});

      // Create chats table
      await supabase.rpc('create_chats_table', {});

      // Create messages table
      await supabase.rpc('create_messages_table', {});

      // Create user_settings table
      await supabase.rpc('create_user_settings_table', {});

      console.log('Database tables initialized successfully');
    } catch (error) {
      console.log('Tables may already exist or initialization failed:', error);
    }
  }

  // User operations
  async getUser(id: string): Promise<SupabaseUser | undefined> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching user:', error)
      return undefined
    }

    return data
  }

  async upsertUser(userData: Partial<SupabaseUser> & { id: string }): Promise<SupabaseUser> {
    const { data, error } = await supabase
      .from('profiles')
      .upsert({
        ...userData,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      throw new Error(`Failed to upsert user: ${error.message}`)
    }
    
    return data
  }

  // Chat operations
  async getChats(userId: string): Promise<SupabaseChat[]> {
    try {
      const { data, error } = await supabase
        .from('chats')
        .select('*')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false })

      if (error) {
        console.error('Error fetching chats:', error);
        return [];
      }

      return data || []
    } catch (error) {
      console.error('Error fetching chats:', error);
      return [];
    }
  }

  async getChat(id: number, userId: string): Promise<SupabaseChat | undefined> {
    const { data, error } = await supabase
      .from('chats')
      .select('*')
      .eq('id', id)
      .eq('user_id', userId)
      .single()
    
    if (error) {
      console.error('Error fetching chat:', error)
      return undefined
    }
    
    return data
  }

  async createChat(chat: Omit<SupabaseChat, 'id' | 'created_at' | 'updated_at'>): Promise<SupabaseChat> {
    try {
      const { data, error } = await supabase
        .from('chats')
        .insert({
          ...chat,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating chat:', error);
        // Return a mock chat for demo purposes
        return {
          id: Date.now(),
          user_id: chat.user_id,
          title: chat.title,
          type: chat.type,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }

      return data
    } catch (error) {
      console.error('Error creating chat:', error);
      // Return a mock chat for demo purposes
      return {
        id: Date.now(),
        user_id: chat.user_id,
        title: chat.title,
        type: chat.type,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    }
  }

  async updateChat(id: number, userId: string, updateData: Partial<SupabaseChat>): Promise<SupabaseChat | undefined> {
    const { data, error } = await supabase
      .from('chats')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', userId)
      .select()
      .single()
    
    if (error) {
      console.error('Error updating chat:', error)
      return undefined
    }
    
    return data
  }

  async deleteChat(id: number, userId: string): Promise<boolean> {
    const { error } = await supabase
      .from('chats')
      .delete()
      .eq('id', id)
      .eq('user_id', userId)
    
    if (error) {
      console.error('Error deleting chat:', error)
      return false
    }
    
    return true
  }

  // Message operations
  async getMessages(chatId: number): Promise<SupabaseMessage[]> {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('chat_id', chatId)
      .order('created_at', { ascending: true })
    
    if (error) {
      throw new Error(`Failed to fetch messages: ${error.message}`)
    }
    
    return data || []
  }

  async createMessage(message: Omit<SupabaseMessage, 'id' | 'created_at'>): Promise<SupabaseMessage> {
    const { data, error } = await supabase
      .from('messages')
      .insert({
        ...message,
        created_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      throw new Error(`Failed to create message: ${error.message}`)
    }
    
    return data
  }

  // User settings operations
  async getUserSettings(userId: string): Promise<SupabaseUserSettings | undefined> {
    try {
      const { data, error } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error) {
        console.error('Error fetching user settings:', error)
        // Return default settings for demo
        return {
          id: 1,
          user_id: userId,
          dark_mode: false,
          auto_save_chats: true,
          send_on_enter: true,
          openai_api_key: undefined,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }

      return data
    } catch (error) {
      console.error('Error fetching user settings:', error)
      // Return default settings for demo
      return {
        id: 1,
        user_id: userId,
        dark_mode: false,
        auto_save_chats: true,
        send_on_enter: true,
        openai_api_key: undefined,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    }
  }

  async upsertUserSettings(settingsData: Partial<SupabaseUserSettings> & { user_id: string }): Promise<SupabaseUserSettings> {
    const { data, error } = await supabase
      .from('user_settings')
      .upsert({
        ...settingsData,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      throw new Error(`Failed to upsert user settings: ${error.message}`)
    }
    
    return data
  }
}

export const supabaseStorage = new SupabaseStorage()