import { useEffect, useState, useRef } from "react";
import { useRout<PERSON>, <PERSON> } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { ArrowLeft, MoreVertical } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ChatBubble } from "@/components/ChatBubble";
import { ChatInput } from "@/components/ChatInput";
import { useAuth0Integration } from "@/hooks/useAuth0";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { Chat, Message } from "@shared/schema";

export default function Chat() {
  const [, params] = useRoute("/chat/:id");
  const chatId = params?.id ? parseInt(params.id) : null;
  const { user } = useAuth0Integration();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isTyping, setIsTyping] = useState(false);

  const { data: chat } = useQuery<Chat>({
    queryKey: ["/api/chats", chatId],
    enabled: !!chatId,
  });

  const { data: messages = [], isLoading: messagesLoading } = useQuery<Message[]>({
    queryKey: ["/api/chats", chatId, "messages"],
    enabled: !!chatId,
  });

  const createMessageMutation = useMutation({
    mutationFn: async (data: { role: "user" | "assistant"; content: string; imageUrl?: string }) => {
      const response = await apiRequest("POST", `/api/chats/${chatId}/messages`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/chats", chatId, "messages"] });
    },
  });

  const aiChatMutation = useMutation({
    mutationFn: async (message: string) => {
      const response = await apiRequest("POST", "/api/ai/chat", {
        message,
        chatId,
      });
      return response.json();
    },
    onSuccess: (data) => {
      // Add AI response to messages
      createMessageMutation.mutate({
        role: "assistant",
        content: data.response,
      });
      setIsTyping(false);
    },
    onError: (error) => {
      setIsTyping(false);
      toast({
        title: "Error",
        description: "Failed to get AI response",
        variant: "destructive",
      });
    },
  });

  const handleSendMessage = async (content: string) => {
    if (!chatId) return;

    // Add user message
    await createMessageMutation.mutateAsync({
      role: "user",
      content,
    });

    // Update chat title if it's the first message
    if (messages.length === 0) {
      const title = content.length > 50 ? content.substring(0, 50) + "..." : content;
      await apiRequest("PUT", `/api/chats/${chatId}`, { title });
      queryClient.invalidateQueries({ queryKey: ["/api/chats"] });
    }

    // Get AI response
    setIsTyping(true);
    aiChatMutation.mutate(content);
  };

  const handleRegenerate = () => {
    if (messages.length > 0) {
      const lastUserMessage = messages
        .filter(m => m.role === "user")
        .pop();
      
      if (lastUserMessage) {
        setIsTyping(true);
        aiChatMutation.mutate(lastUserMessage.content);
      }
    }
  };

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, isTyping]);

  if (!chatId) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            Chat not found
          </h1>
          <Link href="/">
            <Button>Go back to dashboard</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (messagesLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading chat...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* Chat Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center space-x-3">
            <Link href="/">
              <Button variant="ghost" size="sm" className="p-2">
                <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              </Button>
            </Link>
            <h1 className="text-lg font-semibold text-gray-800 dark:text-white truncate">
              {chat?.title || "New Chat"}
            </h1>
          </div>
          <Button variant="ghost" size="sm" className="p-2">
            <MoreVertical className="w-5 h-5 text-gray-600 dark:text-gray-300" />
          </Button>
        </div>
      </header>

      {/* Chat Messages Container */}
      <div className="flex-1 overflow-y-auto px-4 py-4 space-y-4">
        {messages.length === 0 && (
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center flex-shrink-0">
              <span className="text-white text-sm">🤖</span>
            </div>
            <div className="flex-1">
              <div className="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-100 border border-gray-200 dark:border-gray-600 rounded-2xl rounded-tl-md px-4 py-3 max-w-sm">
                <p className="text-sm leading-relaxed">
                  Hello! How can I help you today? I can assist with text generation, image creation, and code development.
                </p>
              </div>
              <p className="text-xs text-gray-400 dark:text-gray-500 mt-2 ml-1">Just now</p>
            </div>
          </div>
        )}

        {messages.map((message) => (
          <ChatBubble
            key={message.id}
            type={message.role === "user" ? "user" : "ai"}
            content={message.content}
            timestamp={message.createdAt || new Date().toISOString()}
            actions={message.role === "assistant" ? ["copy", "share", "regenerate"] : []}
            imageUrl={message.imageUrl || undefined}
            onRegenerate={message.role === "assistant" ? handleRegenerate : undefined}
          />
        ))}

        {/* Typing Indicator */}
        {isTyping && (
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center flex-shrink-0">
              <span className="text-white text-sm">🤖</span>
            </div>
            <div className="bg-gray-100 dark:bg-gray-700 rounded-2xl rounded-tl-md px-4 py-3">
              <ChatBubble
                type="ai"
                content=""
                timestamp={new Date().toISOString()}
                isLoading={true}
              />
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <ChatInput
        onSend={handleSendMessage}
        onImageGen={() => window.location.href = `/image-generation?chatId=${chatId}`}
        isLoading={createMessageMutation.isPending || aiChatMutation.isPending}
      />
    </div>
  );
}
