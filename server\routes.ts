import type { Express } from "express";
import { createServer, type Server } from "http";
import { supabaseStorage } from "./supabaseStorage";
import { AIService } from "./aiService";
import { authService } from "./authService";
import { initializeDatabase } from "./initDatabase";
import { z } from "zod";

// Middleware to check for user authentication
const requireAuth = (req: any, res: any, next: any) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: "Authentication required" });
  }

  const token = authHeader.split(' ')[1];
  const user = authService.verifyToken(token);

  if (!user) {
    return res.status(401).json({ message: "Invalid token" });
  }

  req.userId = user.id;
  req.user = user;
  next();
};

export async function registerRoutes(app: Express): Promise<Server> {
  // Initialize database tables
  await initializeDatabase();

  // Health check
  app.get("/api/health", (req, res) => {
    res.json({ status: "ok", timestamp: new Date().toISOString() });
  });

  // Auth routes
  app.post('/api/auth/login', async (req, res) => {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        return res.status(400).json({ message: "Email and password required" });
      }

      const { user, token } = await authService.loginWithEmail(email, password);
      res.json({ user, token });
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({ message: "Login failed" });
    }
  });

  // Google OAuth login
  app.post('/api/auth/google', async (req, res) => {
    try {
      const { token: googleToken } = req.body;

      if (!googleToken) {
        return res.status(400).json({ message: "Google token required" });
      }

      const { user, token } = await authService.loginWithGoogle(googleToken);
      res.json({ user, token });
    } catch (error) {
      console.error("Google login error:", error);
      res.status(500).json({ message: "Google login failed" });
    }
  });

  app.get('/api/auth/user', requireAuth, async (req: any, res) => {
    try {
      const user = await authService.getUser(req.userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json(user);
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // Chat routes
  app.get("/api/chats", requireAuth, async (req: any, res) => {
    try {
      const chats = await supabaseStorage.getChats(req.userId);
      res.json(chats);
    } catch (error) {
      console.error("Error fetching chats:", error);
      res.status(500).json({ message: "Failed to fetch chats" });
    }
  });

  app.get("/api/chats/:id", requireAuth, async (req: any, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }
      
      const chat = await supabaseStorage.getChat(chatId, req.userId);
      if (!chat) {
        return res.status(404).json({ message: "Chat not found" });
      }
      
      res.json(chat);
    } catch (error) {
      console.error("Error fetching chat:", error);
      res.status(500).json({ message: "Failed to fetch chat" });
    }
  });

  app.post("/api/chats", requireAuth, async (req: any, res) => {
    try {
      const chatData = {
        user_id: req.userId,
        title: req.body.title || 'New Chat',
        type: req.body.type || 'text'
      };
      
      const chat = await supabaseStorage.createChat(chatData);
      res.json(chat);
    } catch (error) {
      console.error("Error creating chat:", error);
      res.status(500).json({ message: "Failed to create chat" });
    }
  });

  app.patch("/api/chats/:id", requireAuth, async (req: any, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }
      
      const updatedChat = await supabaseStorage.updateChat(chatId, req.userId, req.body);
      if (!updatedChat) {
        return res.status(404).json({ message: "Chat not found" });
      }
      
      res.json(updatedChat);
    } catch (error) {
      console.error("Error updating chat:", error);
      res.status(500).json({ message: "Failed to update chat" });
    }
  });

  app.delete("/api/chats/:id", requireAuth, async (req: any, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }
      
      const deleted = await supabaseStorage.deleteChat(chatId, req.userId);
      if (!deleted) {
        return res.status(404).json({ message: "Chat not found" });
      }
      
      res.json({ message: "Chat deleted successfully" });
    } catch (error) {
      console.error("Error deleting chat:", error);
      res.status(500).json({ message: "Failed to delete chat" });
    }
  });

  // Message routes
  app.get("/api/chats/:chatId/messages", requireAuth, async (req: any, res) => {
    try {
      const chatId = parseInt(req.params.chatId);
      if (isNaN(chatId)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }
      
      // Verify chat belongs to user
      const chat = await supabaseStorage.getChat(chatId, req.userId);
      if (!chat) {
        return res.status(404).json({ message: "Chat not found" });
      }
      
      const messages = await supabaseStorage.getMessages(chatId);
      res.json(messages);
    } catch (error) {
      console.error("Error fetching messages:", error);
      res.status(500).json({ message: "Failed to fetch messages" });
    }
  });

  app.post("/api/chats/:chatId/messages", requireAuth, async (req: any, res) => {
    try {
      const chatId = parseInt(req.params.chatId);
      if (isNaN(chatId)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }
      
      // Verify chat belongs to user
      const chat = await supabaseStorage.getChat(chatId, req.userId);
      if (!chat) {
        return res.status(404).json({ message: "Chat not found" });
      }
      
      const messageData = {
        chat_id: chatId,
        role: req.body.role,
        content: req.body.content,
        image_url: req.body.image_url,
        metadata: req.body.metadata
      };
      
      const message = await supabaseStorage.createMessage(messageData);
      res.json(message);
    } catch (error) {
      console.error("Error creating message:", error);
      res.status(500).json({ message: "Failed to create message" });
    }
  });

  // AI Generation routes
  app.post("/api/ai/chat", requireAuth, async (req: any, res) => {
    try {
      const { prompt, conversationHistory } = req.body;
      
      if (!prompt || typeof prompt !== 'string') {
        return res.status(400).json({ message: "Prompt is required" });
      }

      // Try user's API key first, fallback to system key
      const userSettings = await supabaseStorage.getUserSettings(req.userId);
      const apiKey = userSettings?.openai_api_key || process.env.OPENAI_API_KEY;
      
      if (!apiKey) {
        return res.status(400).json({ 
          message: "OpenAI API key not configured. Please add your API key in settings." 
        });
      }

      const aiService = new AIService(apiKey);
      const response = await aiService.generateTextResponse(prompt, conversationHistory || []);
      
      res.json({ response });
    } catch (error) {
      console.error("Error generating AI response:", error);
      if (error instanceof Error && error.message.includes('API key')) {
        return res.status(400).json({ message: "Invalid OpenAI API key. Please check your settings." });
      }
      res.status(500).json({ message: "Failed to generate AI response" });
    }
  });

  app.post("/api/ai/code", requireAuth, async (req: any, res) => {
    try {
      const { prompt, language } = req.body;
      
      if (!prompt || typeof prompt !== 'string') {
        return res.status(400).json({ message: "Prompt is required" });
      }

      const userSettings = await supabaseStorage.getUserSettings(req.userId);
      const apiKey = userSettings?.openai_api_key || process.env.OPENAI_API_KEY;
      
      if (!apiKey) {
        return res.status(400).json({ 
          message: "OpenAI API key not configured. Please add your API key in settings." 
        });
      }

      const aiService = new AIService(apiKey);
      const response = await aiService.generateCodeResponse(prompt, language);
      
      res.json({ response });
    } catch (error) {
      console.error("Error generating code:", error);
      if (error instanceof Error && error.message.includes('API key')) {
        return res.status(400).json({ message: "Invalid OpenAI API key. Please check your settings." });
      }
      res.status(500).json({ message: "Failed to generate code" });
    }
  });

  app.post("/api/ai/image", requireAuth, async (req: any, res) => {
    try {
      const { prompt, size } = req.body;
      
      if (!prompt || typeof prompt !== 'string') {
        return res.status(400).json({ message: "Prompt is required" });
      }

      const userSettings = await supabaseStorage.getUserSettings(req.userId);
      const apiKey = userSettings?.openai_api_key || process.env.OPENAI_API_KEY;
      
      if (!apiKey) {
        return res.status(400).json({ 
          message: "OpenAI API key not configured. Please add your API key in settings." 
        });
      }

      const aiService = new AIService(apiKey);
      const imageUrl = await aiService.generateImage(prompt, size);
      
      res.json({ imageUrl });
    } catch (error) {
      console.error("Error generating image:", error);
      if (error instanceof Error && error.message.includes('API key')) {
        return res.status(400).json({ message: "Invalid OpenAI API key. Please check your settings." });
      }
      res.status(500).json({ message: "Failed to generate image" });
    }
  });

  // Settings routes
  app.get("/api/user/settings", requireAuth, async (req: any, res) => {
    try {
      const settings = await supabaseStorage.getUserSettings(req.userId);
      res.json(settings);
    } catch (error) {
      console.error("Error fetching user settings:", error);
      res.status(500).json({ message: "Failed to fetch user settings" });
    }
  });

  app.post("/api/user/settings", requireAuth, async (req: any, res) => {
    try {
      const settingsData = {
        user_id: req.userId,
        dark_mode: req.body.dark_mode || false,
        auto_save_chats: req.body.auto_save_chats || true,
        send_on_enter: req.body.send_on_enter || true,
        openai_api_key: req.body.openai_api_key
      };
      
      const settings = await supabaseStorage.upsertUserSettings(settingsData);
      res.json(settings);
    } catch (error) {
      console.error("Error updating user settings:", error);
      res.status(500).json({ message: "Failed to update user settings" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}